import { liveStreamingApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  streamingData: null,
  isStreaming: false,
  streamToken: null,
  roomName: null,
  sessionId: null,
  error: null,
  chatMessages: [],
  chatLoading: false,
  chatError: null
};

export const teacherLiveStreamingSlice = liveStreamingApi.injectEndpoints({
  endpoints: (builder) => ({
    startEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/start',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Start Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    stopEnhancedStream: builder.mutation({
      query: (body) => ({
        url: '/api/enhanced-stream/stop',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Stop Enhanced Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    sendChatMessage: builder.mutation({
      query: (body) => ({
        url: '/api/chat/send',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['LiveStreaming']
    }),
    getChatHistory: builder.query({
      query: (sessionId) => `/api/chat/history/${sessionId}`,
      transformResponse: (response) => {
        console.log('Chat History Response:', response);
        return response.messages || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['LiveStreaming']
    }),
    uploadCbtPaper: builder.mutation({
      query: (formData) => ({
        url: '/upload_cbt_paper',
        method: 'POST',
        body: formData,
        formData: true
      }),
      transformResponse: (response) => {
        console.log('Upload CBT Paper Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

const LiveStreamingSlice = createSlice({
  name: 'liveStreaming',
  initialState,
  reducers: {
    setStreamingData: (state, action) => {
      state.streamingData = action.payload;
    },
    setIsStreaming: (state, action) => {
      state.isStreaming = action.payload;
    },
    setStreamToken: (state, action) => {
      state.streamToken = action.payload;
    },
    setRoomName: (state, action) => {
      state.roomName = action.payload;
    },
    setSessionId: (state, action) => {
      state.sessionId = action.payload;
      // Store session ID in session storage when it's set
      if (action.payload) {
        sessionStorage.setItem('streamSessionId', action.payload);
      }
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setChatMessages: (state, action) => {
      state.chatMessages = action.payload;
    },
    addChatMessage: (state, action) => {
      state.chatMessages.push(action.payload);
    },
    setChatLoading: (state, action) => {
      state.chatLoading = action.payload;
    },
    setChatError: (state, action) => {
      state.chatError = action.payload;
    },
    clearStreamingData: (state) => {
      state.streamingData = null;
      state.isStreaming = false;
      state.streamToken = null;
      state.roomName = null;
      state.sessionId = null;
      state.error = null;
      state.chatMessages = [];
      state.chatLoading = false;
      state.chatError = null;
      // Clear session ID from session storage when clearing data
      sessionStorage.removeItem('streamSessionId');
    },
    loadSessionFromStorage: (state) => {
      // Load session ID from session storage on app initialization
      const storedSessionId = sessionStorage.getItem('streamSessionId');
      if (storedSessionId) {
        state.sessionId = storedSessionId;
        state.isStreaming = true; // Assume streaming if session exists
      }
    }
  }
});

export const {
  setStreamingData,
  setIsStreaming,
  setStreamToken,
  setRoomName,
  setSessionId,
  setError,
  setChatMessages,
  addChatMessage,
  setChatLoading,
  setChatError,
  clearStreamingData,
  loadSessionFromStorage
} = LiveStreamingSlice.actions;

export default LiveStreamingSlice.reducer;

export const {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useGetChatHistoryQuery,
  useLazyGetChatHistoryQuery,
  useUploadCbtPaperMutation
} = teacherLiveStreamingSlice;
